// React core
import { useState, useEffect, useCallback } from "react";
// Third-party library imports
// Services
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
// Components
import { <PERSON><PERSON> } from "./components/views/Cookie/Cookie";
import { Welcome } from "./components/views/Welcome/Welcome";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import { GameStartScreen } from "./components/GameStartScreen";
import { RulesPopup } from "./components/RulesPopup";
import { GameEndScreen } from "./components/GameEndScreen";
// Utils & Constants & Helpers
import { GameStep, type GameStepType, GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { checkCookieConsent, saveCookieConsent } from "./utils/cookieUtils";
// Styles
import "./App.scss";

function App() {
  // Step management - Initialize based on cookie consent
  const [currentStep, setCurrentStep] = useState<GameStepType>(() => {
    return checkCookieConsent() ? GameStep.WELCOME : GameStep.COOKIE_BANNER;
  });
  const [showRulesPopup, setShowRulesPopup] = useState<boolean>(false);

  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [gameWon, setGameWon] = useState<boolean>(false);
  const [, setCharacterError] = useState<string>("");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        const audioStartedCallback = appService.getAudioStartedCallback();

        playAudioWithFallback(
          audioUrl,
          audioFinishedCallback, // onEnded
          audioStartedCallback, // onStarted
          (error: string) => {
            // onError
            console.error("❌ Error reproduciendo audio:", error);
            if (audioFinishedCallback) {
              setTimeout(() => {
                audioFinishedCallback();
              }, 1000);
            }
          }
        );
      }, 100);
    });
  }, [appService]);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback(
    (response: any, fallback = "Respuesta no encontrada") => {
      return (
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        fallback
      );
    },
    []
  );

  /*********************************************************************************************
   * Step navigation functions
   *********************************************************************************************/

  const handleAcceptCookies = useCallback(() => {
    saveCookieConsent();
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleDeclineCookies = useCallback(() => {
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleAcceptAndPlay = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
  }, []);

  const handleShowRules = useCallback(() => {
    setShowRulesPopup(true);
  }, []);

  const handleCloseRules = useCallback(() => {
    setShowRulesPopup(false);
  }, []);

  /*********************************************************************************************
   * Game logic functions
   *********************************************************************************************/

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);
    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError(
        "Error al generar personaje o iniciar juego. Inténtalo de nuevo."
      );
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Handle start game from main menu
   * Transitions to game playing step and starts the game
   */
  const handleStartGame = useCallback(() => {
    setCurrentStep(GameStep.GAME_PLAYING);
    handleStartGameDirectly();
  }, [handleStartGameDirectly]);

  /**
   * Handle game end
   * Transitions to game end step and stores the result
   */
  const handleGameEnd = useCallback((won: boolean) => {
    setGameWon(won);
    setCurrentStep(GameStep.GAME_END);
  }, []);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setGameWon(false);
    setCharacterError("");
    setShowRulesPopup(false);
    conversationStorage.clearAllConversations();
  }, [conversationStorage]);

  /*********************************************************************************************
   * Render step content based on current step
   *********************************************************************************************/

  const renderStepContent = () => {
    switch (currentStep) {
      case GameStep.COOKIE_BANNER:
        return <Cookie
          onAccept={handleAcceptCookies}
          onDecline={handleDeclineCookies}
        />;

      case GameStep.WELCOME:
        return (
          <Welcome
            handleAcceptAndPlay={handleAcceptAndPlay}
            aiLoading={aiLoading}
          />
        );

      case GameStep.MAIN_MENU:
        return (
          <GameStartScreen
            onStartGame={handleStartGame}
            onShowRules={handleShowRules}
            onGameEnd={handleGameEnd}
            aiLoading={aiLoading}
          />
        );

      case GameStep.GAME_PLAYING:
        return (
          <div className="card">
            <div className="game-container">
              <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

              {/* Voice Chat Component - handles all conversation history */}
              <SimpleVoiceChat
                generatedCharacter={generatedCharacter}
                isGameStarted={gameStarted}
                initialMessage={initialMessage}
                onGameEnd={handleGameEnd}
              />

              {/* Reset Game Section */}
              {(generatedCharacter || gameStarted) && (
                <div className="reset-section">
                  <button onClick={handleResetGame} className="reset-button">
                    🔄 Reiniciar Juego
                  </button>
                </div>
              )}
            </div>
          </div>
        );

      case GameStep.GAME_END:
        return (
          <GameEndScreen
            gameWon={gameWon}
            onClose={handleResetGame}
            onNewGame={handleResetGame}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {renderStepContent()}

      {/* Rules Popup */}
      <RulesPopup
        isOpen={showRulesPopup}
        onClose={handleCloseRules}
      />
    </>
  );
}

export default App;
